Rails.application.routes.draw do
  # Order and payment routes
  resources :orders, only: [ :create ] do
    collection do
      get :success
      get :cancel
    end
  end

  # Define your application routes per the DSL in https://guides.rubyonrails.org/routing.html

  # Reveal health status on /up that returns 200 if the app boots with no exceptions, otherwise 500.
  # Can be used by load balancers and uptime monitors to verify that the app is live.
  get "up" => "rails/health#show", as: :rails_health_check

  # Render dynamic PWA files from app/views/pwa/* (remember to link manifest in application.html.erb)
  # get "manifest" => "rails/pwa#manifest", as: :pwa_manifest
  # get "service-worker" => "rails/pwa#service_worker", as: :pwa_service_worker

  # scope "(:locale)", locale: /#{I18n.available_locales.join("|")}/ do
  # 定义应用程序的根路径。
  root "home#index"

  # 以用户为中心的个人资料和设置路由。
  # resources :users, only: [ :show, :edit, :update ]
  # get "/@:username", to: "users#show", as: :user_profile
  # get "/@:username/edit", to: "users#edit", as: :edit_user_profile
  # patch "/@:username", to: "users#update", as: :update_user_profile

  # 当前用户的个人资料路由
  get "/profile", to: "users#profile", as: :profile
  get "/profile/edit", to: "users#edit_profile", as: :edit_profile
  patch "/profile", to: "users#update_profile", as: :update_profile

  # Plans and pricing
  resources :plans, only: [ :index ]

  # Music generation
  resources :generations, only: [ :create, :destroy ] do
    collection do
      post "generate_lyrics"
      get "random_prompts"
      get "presets"
      get "search_presets"
    end
  end

  # Songs
  resources :songs, only: [ :show, :edit, :update, :destroy ] do
    member do
      patch :toggle_favorite
    end
  end

  # 用于用户登录和登出的会话管理。
  resources :sessions, only: [] do
    # 身份验证路由，包括基于电子邮件的 OTP 和社交登录。
    collection do
      post :request_otp
      post :verify_otp
      get :google
      get :google_callback
    end
  end

  get "/ai-music-generator", to: "songs#index", as: :songs
  get "/login", to: "sessions#new", as: :login
  delete "/logout", to: "sessions#destroy", as: :logout

  # 更新 OmniAuth 回调路径，使用与 OmniAuth.config.path_prefix 相同的前缀
  get "/auth/failure", to: "sessions#auth_failure"

  post "/webhooks/stripe", to: "webhooks#stripe", defaults: { format: :json }
  post "/webhooks/apibox", to: "webhooks#apibox", defaults: { format: :json }
end
