# This file is auto-generated from the current state of the database. Instead
# of editing this file, please use the migrations feature of Active Record to
# incrementally modify your database, and then regenerate this schema definition.
#
# This file is the source Rails uses to define your schema when running `bin/rails
# db:schema:load`. When creating a new database, `bin/rails db:schema:load` tends to
# be faster and is potentially less error prone than running all of your
# migrations from scratch. Old migrations may fail to apply correctly if those
# migrations use external dependencies or application code.
#
# It's strongly recommended that you check this file into your version control system.

ActiveRecord::Schema[8.0].define(version: 2025_07_01_082251) do
  # These are extensions that must be enabled in order to support this database
  enable_extension "pg_catalog.plpgsql"

  create_table "generation_tasks", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.integer "status", default: 0, null: false
    t.string "api_version", null: false
    t.jsonb "generation_params", null: false
    t.jsonb "task_data"
    t.datetime "completed_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "task_id"
    t.jsonb "error_data"
    t.datetime "timeout_at"
    t.index ["completed_at"], name: "index_generation_tasks_on_completed_at"
    t.index ["status"], name: "index_generation_tasks_on_status"
    t.index ["task_id"], name: "index_generation_tasks_on_task_id"
    t.index ["timeout_at"], name: "index_generation_tasks_on_timeout_at"
    t.index ["user_id"], name: "index_generation_tasks_on_user_id"
  end

  create_table "identities", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.string "provider"
    t.string "uid"
    t.string "email"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["email"], name: "index_identities_on_email"
    t.index ["provider", "uid"], name: "index_identities_on_provider_and_uid", unique: true
    t.index ["user_id", "provider"], name: "index_identities_on_user_id_and_provider", unique: true
    t.index ["user_id"], name: "index_identities_on_user_id"
  end

  create_table "orders", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.bigint "plan_id", null: false
    t.string "stripe_session_id"
    t.integer "status", default: -1, null: false
    t.bigint "amount", null: false
    t.string "currency", null: false
    t.datetime "paid_time"
    t.jsonb "metadata", default: {}, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["paid_time"], name: "index_orders_on_paid_time"
    t.index ["plan_id"], name: "index_orders_on_plan_id"
    t.index ["status"], name: "index_orders_on_status"
    t.index ["stripe_session_id"], name: "index_orders_on_stripe_session_id", unique: true
    t.index ["user_id", "status"], name: "index_orders_on_user_id_and_status"
    t.index ["user_id"], name: "index_orders_on_user_id"
  end

  create_table "plans", force: :cascade do |t|
    t.string "name", null: false
    t.text "description"
    t.string "stripe_product_id", null: false
    t.string "stripe_price_id", null: false
    t.boolean "active", default: true, null: false
    t.integer "unit_amount", null: false
    t.string "currency", default: "usd", null: false
    t.integer "billing_interval", null: false
    t.jsonb "plan_details", default: {}, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["active"], name: "index_plans_on_active"
    t.index ["billing_interval"], name: "index_plans_on_billing_interval"
    t.index ["stripe_price_id"], name: "index_plans_on_stripe_price_id", unique: true
    t.index ["stripe_product_id"], name: "index_plans_on_stripe_product_id"
  end

  create_table "sessions", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.string "ip_address"
    t.string "user_agent"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["user_id"], name: "index_sessions_on_user_id"
  end

  create_table "songs", id: :string, force: :cascade do |t|
    t.integer "user_id", null: false
    t.integer "generation_task_id", null: false
    t.boolean "favorite", default: false, null: false
    t.string "audio_url"
    t.string "image_url"
    t.jsonb "lyrics"
    t.jsonb "metadata", default: {}, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "stream_audio_url"
    t.index "((metadata ->> 'artist'::text))", name: "index_songs_on_metadata_artist"
    t.index "((metadata ->> 'genre'::text))", name: "index_songs_on_metadata_genre"
    t.index "((metadata ->> 'style'::text))", name: "index_songs_on_metadata_style"
    t.index "((metadata ->> 'title'::text))", name: "index_songs_on_metadata_title"
    t.index ["created_at"], name: "index_songs_on_created_at"
    t.index ["favorite"], name: "index_songs_on_favorite"
    t.index ["generation_task_id"], name: "index_songs_on_generation_task_id"
    t.index ["metadata"], name: "index_songs_on_metadata", using: :gin
    t.index ["user_id", "created_at"], name: "index_songs_on_user_id_and_created_at"
    t.index ["user_id", "favorite"], name: "index_songs_on_user_id_and_favorite"
    t.index ["user_id"], name: "index_songs_on_user_id"
  end

  create_table "stripe_webhook_events", force: :cascade do |t|
    t.string "stripe_event_id", null: false
    t.string "event_type", null: false
    t.string "processing_result", default: "processing", null: false
    t.datetime "processed_at"
    t.text "error_message"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["created_at"], name: "index_stripe_webhook_events_on_created_at"
    t.index ["event_type"], name: "index_stripe_webhook_events_on_event_type"
    t.index ["processing_result"], name: "index_stripe_webhook_events_on_processing_result"
    t.index ["stripe_event_id"], name: "index_stripe_webhook_events_on_stripe_event_id", unique: true
  end

  create_table "subscriptions", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.bigint "plan_id", null: false
    t.string "stripe_subscription_id", null: false
    t.integer "status", null: false
    t.datetime "current_period_start"
    t.datetime "current_period_end"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["plan_id"], name: "index_subscriptions_on_plan_id"
    t.index ["status"], name: "index_subscriptions_on_status"
    t.index ["stripe_subscription_id"], name: "index_subscriptions_on_stripe_subscription_id", unique: true
    t.index ["user_id", "status"], name: "index_subscriptions_on_user_id_and_status"
    t.index ["user_id"], name: "index_subscriptions_on_user_id"
  end

  create_table "users", force: :cascade do |t|
    t.string "username", null: false
    t.integer "extra_credits", default: 0, null: false
    t.integer "plan_credits_used", default: 0, null: false
    t.integer "plan_credits_limit", default: 0, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "plan_type", default: 0, null: false
    t.string "email", null: false
    t.index "lower((username)::text)", name: "index_users_on_lower_username", unique: true
    t.index "lower((username)::text)", name: "index_users_on_username_lower", unique: true
    t.index ["email"], name: "index_users_on_email", unique: true
    t.index ["username"], name: "index_users_on_username"
  end

  add_foreign_key "generation_tasks", "users"
  add_foreign_key "identities", "users"
  add_foreign_key "orders", "plans"
  add_foreign_key "orders", "users"
  add_foreign_key "sessions", "users"
  add_foreign_key "songs", "generation_tasks"
  add_foreign_key "songs", "users"
  add_foreign_key "subscriptions", "plans"
  add_foreign_key "subscriptions", "users"
end
