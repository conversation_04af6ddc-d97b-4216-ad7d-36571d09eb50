class StripeWebhookEventCleanupJob < ApplicationJob
  queue_as :daily_tasks

  def perform
    Rails.logger.info "🧹 开始清理过期的Stripe webhook事件记录"

    cleanup_expired_events

    Rails.logger.info "✅ Stripe webhook事件清理任务完成"
  end

  private

  def cleanup_expired_events
    # 查找超过24小时的过期事件记录
    expired_events = StripeWebhookEvent.expired
    total_count = expired_events.count

    Rails.logger.info "📊 找到 #{total_count} 个过期的webhook事件记录"

    if total_count == 0
      Rails.logger.info "ℹ️ 没有过期的webhook事件需要清理"
      return
    end

    begin
      # 执行批量删除
      deleted_count = StripeWebhookEvent.cleanup_expired!

      Rails.logger.info "✅ 成功清理 #{deleted_count} 个过期的webhook事件记录"
    rescue => e
      Rails.logger.error "❌ 清理webhook事件记录失败: #{e.message}"
      Rails.logger.error e.backtrace.join("\n")
      raise e
    end
  end
end
