class ProCreditResetJob < ApplicationJob
  queue_as :daily_tasks

  def perform
    Rails.logger.info "🔄 开始执行Pro用户积分重置任务"

    reset_pro_users_credits

    Rails.logger.info "✅ Pro用户积分重置任务完成"
  end

  private

  def reset_pro_users_credits
    Rails.logger.info "💎 开始重置Pro用户积分"

    # 重置月度订阅的Pro用户积分
    reset_monthly_pro_users

    # 重置年度订阅的Pro用户积分
    reset_yearly_pro_users
  end

  def reset_monthly_pro_users
    Rails.logger.info "📅 开始重置月度订阅Pro用户积分"

    # 查找需要重置积分的月度订阅Pro用户
    # 条件：用户为Pro，订阅状态为active，计划为月度，且下次重置时间已到
    monthly_users_query = User.pro
                              .joins(subscription: :plan)
                              .where(subscriptions: { status: :active })
                              .where(plans: { billing_interval: :month })
                              .where("subscriptions.current_period_start + INTERVAL '1 month' <= ?", Time.current)
                              .where("users.plan_credits_used > 0")

    reset_pro_users_by_query(monthly_users_query, "月度订阅")
  end

  def reset_yearly_pro_users
    Rails.logger.info "📅 开始重置年度订阅Pro用户积分"

    # 查找需要重置积分的年度订阅Pro用户
    # 条件：用户为Pro，订阅状态为active，计划为年度，且下次重置时间已到
    yearly_users_query = User.pro
                             .joins(subscription: :plan)
                             .where(subscriptions: { status: :active })
                             .where(plans: { billing_interval: :year })
                             .where("subscriptions.current_period_start + INTERVAL '1 year' <= ?", Time.current)
                             .where("users.plan_credits_used > 0")

    reset_pro_users_by_query(yearly_users_query, "年度订阅")
  end

  def reset_pro_users_by_query(users_query, subscription_type)
    total_count = users_query.count

    Rails.logger.info "📊 找到 #{total_count} 个#{subscription_type}Pro用户需要重置积分"

    if total_count == 0
      Rails.logger.info "ℹ️ 没有#{subscription_type}Pro用户需要重置积分"
      return
    end

    begin
      updated_count = users_query.update_all(
        plan_credits_used: 0,
        updated_at: Time.current
      )

      Rails.logger.info "✅ #{subscription_type}Pro用户积分重置完成: 成功重置 #{updated_count} 个用户的积分"
    rescue => e
      Rails.logger.error "❌ 批量重置#{subscription_type}Pro用户积分失败: #{e.message}"
      Rails.logger.error e.backtrace.join("\n")
    end
  end
end
