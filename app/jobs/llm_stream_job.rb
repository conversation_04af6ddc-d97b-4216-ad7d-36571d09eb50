# Job for handling LLM streaming responses via Turbo Stream
class LlmStreamJob < ApplicationJob
  include ApplicationHelper

  queue_as :default

  # Discard job on specific errors to avoid retries
  discard_on ActiveJob::DeserializationError
  discard_on StandardError

  def perform(user_id, stream_type, target_id, **options)
    @user = User.find(user_id)
    @stream_type = stream_type
    @target_id = target_id
    @options = options
    @request_id = options[:request_id]

    # Validate request ID is present
    unless @request_id.present?
      Rails.logger.error "❌ LLM stream job failed: Missing request ID"
      return
    end

    Rails.logger.info "🌊 Starting LLM stream job: type=#{stream_type}, target=#{target_id}, user=#{user_id}, request_id=#{@request_id}"

    case stream_type
    when "lyrics"
      generate_lyrics_stream
    else
      Rails.logger.error "❌ Unknown stream type: #{stream_type}"
      broadcast_error("Unknown stream type")
    end
  rescue => e
    Rails.logger.error "❌ LLM stream job failed: #{e.message}"
    Rails.logger.error e.backtrace.join("\n")
    broadcast_error("Generation failed. Please try again.")
  ensure
    restore_button_state
  end

  private

  attr_reader :user, :stream_type, :target_id, :options, :request_id

  # Generate lyrics using streaming service
  def generate_lyrics_stream
    content_buffer = ""

    service = LyricsStreamService.new(
      style: options[:style]
    )

    service.generate_lyrics do |chunk|
      content_buffer += chunk
      update_textarea_content(content_buffer)
    end

    Rails.logger.info "✅ Lyrics generation completed"
  end

  # Update textarea content via Turbo Stream
  def update_textarea_content(content)
    Turbo::StreamsChannel.broadcast_action_to(
      user,
      llm_stream_channel_id(request_id),
      target: target_id,
      action: "llm_update",
      partial: "shared/streaming_textarea",
      locals: {
        content: content,
        target_id: target_id,
        maxlength: maxlength_for_target
      }
    )

    # Update character count
    update_char_count(content.length)
  rescue => e
    Rails.logger.warn "⚠️ Failed to update textarea content: #{e.message}"
    # Don't raise to avoid breaking the stream
  end

  # Update character count display
  def update_char_count(count)
    char_count_id = "#{target_id}_char_count"

    Turbo::StreamsChannel.broadcast_update_to(
      user, llm_stream_channel_id(request_id),
      target: char_count_id,
      content: "#{count}/#{maxlength_for_target} characters"
    )
  rescue => e
    Rails.logger.warn "⚠️ Failed to update character count: #{e.message}"
    # Don't raise to avoid breaking the stream
  end

  # Broadcast error message to textarea
  def broadcast_error(message)
    Turbo::StreamsChannel.broadcast_action_to(
      user,
      llm_stream_channel_id(request_id),
      target: target_id,
      action: "llm_update",
      partial: "shared/streaming_textarea",
      locals: {
        content: "❌ #{message}",
        target_id: target_id,
        maxlength: maxlength_for_target
      }
    )
  rescue => e
    Rails.logger.error "❌ Failed to broadcast error: #{e.message}"
  end

  # Restore button to normal state
  def restore_button_state
    Turbo::StreamsChannel.broadcast_update_to(
      user,
      llm_stream_channel_id(request_id),
      target: :lyrics_gen_button,
      partial: "shared/lyrics_generation_button_submit"
    )
  rescue => e
    Rails.logger.warn "⚠️ Failed to restore button state: #{e.message}"
  end

  # Get maxlength for different target types
  def maxlength_for_target
    case target_id
    when "generation_lyrics"
      3000
    when "generation_description", "song_description"
      200
    else
      1000
    end
  end
end
