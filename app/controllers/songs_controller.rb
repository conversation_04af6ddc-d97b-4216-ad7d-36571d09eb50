class SongsController < ApplicationController
  include PlanConfiguration
  include ApplicationHelper
  before_action :set_song, only: [ :show, :edit, :update, :toggle_favorite, :destroy ]

  PER_PAGE = 10

  def index
    # 分页参数处理
    @current_page = params[:page].to_i.positive? ? params[:page].to_i : 1
    offset = (@current_page - 1) * PER_PAGE

    # 基础查询构建
    base_query = Current.user.songs.joins(:generation_task).includes(:generation_task)

    # 应用搜索
    if params[:search].present?
      base_query = base_query.search_by_title(params[:search])
    end

    # 应用收藏筛选
    if params[:favorites] == "true"
      base_query = base_query.favorited
    end

    # 应用排序
    songs_query = case params[:sort]
    when "created_at"
      base_query.order_by_created_at(params[:order])
    when "title"
      base_query.order_by_title(params[:order])
    when "favorites"
      base_query.order_by_favorites
    else
      # 默认排序：生成中任务优先
      base_query.merge(GenerationTask.order_by_generation_priority)
    end

    # 计算分页信息
    @total_count = songs_query.count
    @total_pages = (@total_count.to_f / PER_PAGE).ceil
    @has_prev = @current_page > 1
    @has_next = @current_page < @total_pages
    @per_page = PER_PAGE

    # 获取当前页数据
    @songs = songs_query.limit(PER_PAGE).offset(offset)

    # 保持现有的生成任务查询
    @generating_tasks = Current.user.generation_tasks.queued.order(created_at: :desc)
    @failed_tasks = Current.user.generation_tasks.failed.order(created_at: :desc)

    # 设置生成表单所需的实例变量
    @request_id = generate_request_id
    @current_credits = Current.user.spendable_credits
    @generation_cost = generation_cost
    @random_prompts = RandomPrompts.random(3)

    respond_to do |format|
      format.html
      format.turbo_stream
    end
  end

  def show
    # Song is set by before_action
  end

  def edit
    # Song is set by before_action
    # Return edit form in modal via Turbo Frame
  end

  def update
    if @song.update_title(song_params[:title])
      respond_to do |format|
        format.html do
          redirect_to @song, notice: "Song title updated successfully!"
        end
        format.turbo_stream { flash.now[:notice] = "Song title updated successfully!" }
      end
    else
      render :edit, status: :unprocessable_entity
    end
  end

  def toggle_favorite
    if @song.toggle_favorite
      respond_to do |format|
        format.html do
          redirect_to @song, notice: @song.favorite? ? "#{@song.title} added to favorites!" : "#{@song.title} removed from favorites!"
        end
        format.turbo_stream { flash.now[:notice] = @song.favorite? ? "#{@song.title} added to favorites!" : "#{@song.title} removed from favorites!" }
      end
    else
      render :show, status: :unprocessable_entity
    end
  end

  def destroy
    if @song.destroy
      respond_to do |format|
        format.html do
          redirect_to songs_path, notice: "#{@song.title} deleted successfully!"
        end
        format.turbo_stream { flash.now[:notice] = "#{@song.title} deleted successfully!" }
      end
    else
      redirect_to songs_path, alert: "Unable to delete song."
    end
  end

  private

  def set_song
    @song = Current.user.songs.find(params[:id])
  rescue ActiveRecord::RecordNotFound
    redirect_to songs_path, alert: "Song not found."
  end

  def song_params
    params.require(:song).permit(:title)
  end
end
