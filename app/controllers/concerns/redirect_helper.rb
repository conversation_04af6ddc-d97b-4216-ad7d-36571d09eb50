module RedirectHelper
  extend ActiveSupport::Concern

  private

  # Validate if the given path is a valid route in the application
  # @param path [String] The path to validate
  # @return [Boolean] true if valid, false otherwise
  def valid_redirect_path?(path)
    Rails.logger.debug("RedirectHelper: Validating path: #{path.inspect}")

    return false if path.blank?
    Rails.logger.debug("RedirectHelper: Path is not blank")

    unless path.start_with?("/")
      Rails.logger.debug("RedirectHelper: Path does not start with '/': #{path}")
      return false
    end
    Rails.logger.debug("RedirectHelper: Path starts with '/'")

    begin
      # Use Rails routing to validate the path
      recognized = Rails.application.routes.recognize_path(path)
      Rails.logger.debug("RedirectHelper: Path recognized successfully: #{recognized}")
      true
    rescue ActionController::RoutingError => e
      Rails.logger.warn("RedirectHelper: Invalid redirect path attempted: #{path} - #{e.message}")
      false
    rescue => e
      Rails.logger.error("RedirectHelper: Unexpected error validating redirect path #{path}: #{e.message}")
      false
    end
  end

  # Store a redirect target in the session after validation
  # @param path [String] The path to store as redirect target
  # @return [Boolean] true if stored successfully, false otherwise
  def store_redirect_target(path)
    Rails.logger.debug("RedirectHelper: Attempting to store redirect target: #{path.inspect}")

    if valid_redirect_path?(path)
      session[:redirect_after_success] = path
      Rails.logger.info("RedirectHelper: Successfully stored redirect target: #{path}")
      Rails.logger.debug("RedirectHelper: Session state after storing: #{session[:redirect_after_success]}")
      true
    else
      Rails.logger.warn("RedirectHelper: Attempted to store invalid redirect target: #{path}")
      false
    end
  end

  # Get and clear the redirect target from session
  # @return [String, nil] The redirect target path or nil if none exists
  def get_and_clear_redirect_target
    Rails.logger.debug("RedirectHelper: Retrieving redirect target from session")
    target = session[:redirect_after_success]
    Rails.logger.debug("RedirectHelper: Found target in session: #{target.inspect}")

    if target.present?
      session.delete(:redirect_after_success)
      Rails.logger.debug("RedirectHelper: Cleared redirect target from session")
    end

    # Double-check the stored target is still valid
    if target.present? && !valid_redirect_path?(target)
      Rails.logger.warn("RedirectHelper: Stored redirect target is no longer valid: #{target}")
      return nil
    end

    Rails.logger.info("RedirectHelper: Retrieved redirect target: #{target}") if target.present?
    Rails.logger.debug("RedirectHelper: No redirect target found") if target.blank?
    target
  end

  # Redirect with flash message, with fallback handling
  # @param target_path [String, nil] The path to redirect to
  # @param fallback_path [String] The fallback path if target is invalid
  # @param flash_type [Symbol] The flash message type (:notice, :alert, etc.)
  # @param message [String] The flash message
  def redirect_with_flash(target_path, fallback_path, flash_type, message)
    Rails.logger.debug("RedirectHelper: redirect_with_flash called with target: #{target_path.inspect}, fallback: #{fallback_path}")

    if target_path.present? && valid_redirect_path?(target_path)
      redirect_path = target_path
      Rails.logger.info("RedirectHelper: Using target path: #{redirect_path}")
    else
      redirect_path = fallback_path
      Rails.logger.info("RedirectHelper: Using fallback path: #{redirect_path} (target was #{target_path.present? ? 'invalid' : 'blank'})")
    end

    flash[flash_type] = message
    Rails.logger.debug("RedirectHelper: Set flash[#{flash_type}] = #{message}")

    redirect_to redirect_path

    Rails.logger.info("RedirectHelper: Redirected to #{redirect_path} with #{flash_type}: #{message}")
  end
end
