class StripeWebhookEvent < ApplicationRecord
  # Validations
  validates :stripe_event_id, presence: true, uniqueness: true
  validates :event_type, presence: true
  validates :processing_result, inclusion: { in: %w[processing success failed] }

  # Scopes
  scope :processed, -> { where(processing_result: [ "success", "failed" ]) }
  scope :expired, -> { where("created_at < ?", 24.hours.ago) }
  scope :successful, -> { where(processing_result: "success") }
  scope :failed, -> { where(processing_result: "failed") }

  # Methods
  def success?
    processing_result == "success"
  end

  def failed?
    processing_result == "failed"
  end

  def processed?
    success? || failed?
  end

  def processing?
    processing_result == "processing"
  end

  # Class methods for cleanup
  def self.cleanup_expired!
    expired.delete_all
  end
end
