# Base service for handling LLM streaming responses
# Provides common functionality for all LLM-based streaming services
class LlmStreamService
  class Error < StandardError; end
  class APIError < Error; end
  class ValidationError < Error; end

  def initialize
    @api_key = Rails.application.credentials.dig(:moleapi, :api_key)
    raise ValidationError, "API key not configured" if @api_key.blank?
  end

  protected

  # Build HTTP connection for streaming requests
  def build_connection
    Faraday.new do |conn|
      conn.options.timeout = 30
      conn.options.read_timeout = 60
      conn.adapter :net_http
    end
  end

  # Process streaming response chunks
  def process_stream_chunk(chunk, accumulated_content, &block)
    # Process Server-Sent Events format
    lines = chunk.split("\n")

    lines.each do |line|
      next unless line.start_with?("data: ")

      data = line[6..-1].strip  # Remove "data: " prefix
      next if data.empty? || data == "[DONE]"

      begin
        parsed_data = JSON.parse(data)
        content = parsed_data.dig("choices", 0, "delta", "content")

        # Process content to ensure proper formatting - only append if content is not nil
        if content
          accumulated_content << content
          block.call(content) if block_given?
        end
      rescue JSON::ParserError => e
        Rails.logger.warn "⚠️ Failed to parse stream chunk: #{e.message}"
        # Continue processing other chunks
      end
    end
  rescue => e
    Rails.logger.error "❌ Error processing stream chunk: #{e.message}"
    # Don't raise here to avoid breaking the stream
  end

  # Build request body for OpenAI API
  def build_request_body(prompt, options = {})
    {
      model: options[:model] || "gpt-4o-mini",
      messages: build_messages(prompt, options),
      max_tokens: options[:max_tokens] || 1000,
      temperature: options[:temperature] || 0.8,
      stream: true
    }
  end

  # Build messages array for chat completion
  def build_messages(prompt, options = {})
    messages = []

    # Add system message if provided
    if options[:system_message]
      messages << {
        role: "system",
        content: options[:system_message]
      }
    end

    # Add user message
    messages << {
      role: "user",
      content: prompt
    }

    messages
  end

  # Stream content from OpenAI API
  def stream_from_api(prompt, options = {}, &block)
    Rails.logger.info "🌊 Starting LLM streaming request"

    connection = build_connection
    accumulated_content = ""

    response = connection.post("https://api.moleapi.com/v1/chat/completions") do |req|
      req.headers["Content-Type"] = "application/json"
      req.headers["Authorization"] = "Bearer #{@api_key}"
      req.body = build_request_body(prompt, options).to_json

      req.options.on_data = proc do |chunk, overall_received_bytes, env|
        process_stream_chunk(chunk, accumulated_content, &block)
      end
    end

    if response.status != 200
      error_message = "API request failed with status #{response.status}"
      Rails.logger.error "❌ #{error_message}"
      raise APIError, error_message
    end

    Rails.logger.info "✅ LLM streaming completed"
    accumulated_content
  rescue Faraday::Error => e
    Rails.logger.error "❌ Faraday error during LLM streaming: #{e.message}"
    raise APIError, "Network error: #{e.message}"
  rescue JSON::ParserError => e
    Rails.logger.error "❌ JSON parsing error: #{e.message}"
    raise APIError, "Invalid API response format"
  rescue => e
    Rails.logger.error "❌ Unexpected error during LLM streaming: #{e.message}"
    Rails.logger.error e.backtrace.join("\n")
    raise Error, "LLM streaming failed: #{e.message}"
  end

  private

  attr_reader :api_key
end
