# Service for streaming lyrics generation using LLM
# Inherits from LlmStreamService for common streaming functionality
class LyricsStreamService < LlmStreamService
  def initialize(style: "")
    super()
    @style = style.to_s.strip
  end

  # Generate lyrics with streaming response
  def generate_lyrics(&block)
    prompt = build_lyrics_prompt

    stream_from_api(
      prompt,
      {
        system_message: system_message,
        model: "gpt-4o-mini",
        max_tokens: 1000,
        temperature: 0.8
      },
      &block
    )
  end

  private

  attr_reader :style

  # Build the lyrics generation prompt
  def build_lyrics_prompt
    prompt_parts = []

    if style.present?
      prompt_parts << "Create song lyrics with the following style specifications:"
      prompt_parts << "Style: #{style}"
    else
      prompt_parts << "Create original song lyrics on any theme you choose."
    end

    prompt_parts << ""
    prompt_parts << "Requirements:"
    prompt_parts << "- Create complete, well-structured lyrics"
    prompt_parts << "- Include verse, chorus, and bridge sections"
    prompt_parts << "- Make the lyrics engaging and memorable"
    prompt_parts << "- Ensure proper rhyme scheme and rhythm"
    prompt_parts << "- Keep the content appropriate for all audiences"
    prompt_parts << "- The lyrics should be in the same language as the style description"
    prompt_parts << "- Include only lyrics content, do not include title, each section label should be in separate lines"
    prompt_parts << ""
    prompt_parts << "Format the output with clear section labels like [Verse 1], [Chorus], [Verse 2], [Bridge], etc."

    prompt_parts.join("\n")
  end

  # System message for lyrics generation
  def system_message
    "You are a creative songwriter and lyricist. Generate high-quality, original song lyrics that are engaging, memorable, and well-structured. Focus on creating lyrics that tell a story or convey emotion effectively. Use proper song structure with verses, choruses, and bridges. Ensure the lyrics have good rhythm and rhyme scheme suitable for singing."
  end
end
