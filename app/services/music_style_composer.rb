class MusicStyleComposer
  class << self
    # Compose style string from individual components
    def compose(genre: [], mood: [], vocal: nil, instrument: [], tpm: nil)
      parts = []

      # Add genre information
      if genre.present? && genre.any?(&:present?)
        parts << genre.reject(&:blank?).join(", ")
      end

      # Add mood information
      if mood.present? && mood.any?(&:present?)
        parts << mood.reject(&:blank?).join(", ")
      end

      # Add vocal information
      if vocal.present? && vocal != ""
        parts << vocal_description(vocal)
      end

      # Add instrument information
      if instrument.present? && instrument.any?(&:present?)
        parts << instrument.reject(&:blank?).join(", ")
      end

      # Add tempo information
      if tpm.present? && tpm != ""
        parts << tpm
      end

      # Join all parts with commas and return
      parts.reject(&:blank?).join(", ")
    end

    # Validate that at least some style components are provided
    def validate_components(genre: [], mood: [], vocal: nil, instrument: [], tpm: nil)
      has_genre = genre.present? && genre.any?(&:present?)
      has_mood = mood.present? && mood.any?(&:present?)
      has_vocal = vocal.present? && vocal != ""
      has_instrument = instrument.present? && instrument.any?(&:present?)
      has_tpm = tpm.present? && tpm != ""

      unless has_genre || has_mood || has_vocal || has_instrument || has_tpm
        raise ArgumentError, "At least one style component (genre, mood, vocal, instrument, or tempo) is required"
      end

      true
    end

    private

    def vocal_description(vocal)
      case vocal.to_s.downcase
      when "male"
        "male vocals"
      when "female"
        "female vocals"
      else
        vocal.to_s
      end
    end
  end
end
