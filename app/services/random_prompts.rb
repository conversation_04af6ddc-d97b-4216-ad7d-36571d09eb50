class RandomPrompts
  class << self
    # Load prompts from YAML configuration
    def prompts
      @prompts ||= YAML.load_file(Rails.root.join("config", "random_prompts.yml"))["prompts"]
    end

    # Get random prompts
    def random(count = 3)
      prompts.sample(count)
    end

    # Get stable recommendations using request ID as seed for consistency
    def stable_random(request_id, count = 3)
      seed = request_id.hash.abs
      prompts.sample(count, random: Random.new(seed))
    end

    # Check if a prompt exists (useful for validation)
    def exists?(prompt_text)
      prompts.any? { |prompt| prompt.downcase == prompt_text.downcase }
    end

    # Get all available prompts
    def all
      prompts
    end

    # Reload prompts (useful for development)
    def reload!
      @prompts = nil
      prompts
    end

    # Get prompts count
    def count
      prompts.size
    end
  end
end
