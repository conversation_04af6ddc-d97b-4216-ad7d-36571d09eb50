class MusicPresets
  class << self
    # Load presets from YAML configuration
    def presets
      @presets ||= YAML.load_file(Rails.root.join("config", "music_presets.yml"))
    end

    # Map singular field types to plural YAML keys
    def yaml_key_for_field(field_type)
      case field_type.to_s
      when "genre"
        "genres"
      when "mood"
        "moods"
      when "instrument"
        "instruments"
      when "vocal"
        "vocal"
      when "tpm"
        "tpm"
      else
        field_type.to_s
      end
    end

    # Search for presets by field type and query
    def search(field_type, query = nil)
      yaml_key = yaml_key_for_field(field_type)
      field_presets = presets[yaml_key]
      return [] unless field_presets

      if query.blank?
        # Return random 5 suggestions when no query
        field_presets.sample(5)
      else
        # Filter presets that match the query (case insensitive)
        query_downcase = query.downcase
        matches = field_presets.select do |preset|
          preset.downcase.include?(query_downcase)
        end

        # Limit results to prevent overwhelming UI
        matches.first(10)
      end
    end

    # Get popular/commonly used presets for a field type (for default display)
    def popular_for_field(field_type)
      popular_presets = {
        "genre" => [ "Pop", "Rock", "Hip Hop", "Electronic", "Jazz" ],
        "mood" => [ "Happy", "Sad", "Energetic", "Calm", "Romantic" ],
        "instrument" => [ "Guitar", "Piano", "Drums", "Bass", "Violin" ]
      }

      # Return popular presets if available, otherwise return first few from all presets
      if popular_presets.key?(field_type.to_s)
        popular_presets[field_type.to_s]
      else
        all_for_field(field_type).first(5)
      end
    end

    # Get random suggestions for a field type
    def random_suggestions(field_type, count = 5)
      yaml_key = yaml_key_for_field(field_type)
      field_presets = presets[yaml_key]
      return [] unless field_presets

      field_presets.sample(count)
    end

    # Get stable recommendations for a field type using request ID as seed
    def stable_recommendations(field_type, request_id, count = 5)
      yaml_key = yaml_key_for_field(field_type)
      field_presets = presets[yaml_key]
      return [] unless field_presets

      # Use request_id as seed for consistent recommendations per request
      seed = request_id.hash.abs
      field_presets.sample(count, random: Random.new(seed))
    end

    # Get all presets for a field type
    def all_for_field(field_type)
      yaml_key = yaml_key_for_field(field_type)
      presets[yaml_key] || []
    end

    # Check if a preset exists in a field
    def preset_exists?(field_type, preset_value)
      yaml_key = yaml_key_for_field(field_type)
      field_presets = presets[yaml_key]
      return false unless field_presets

      field_presets.any? { |preset| preset.downcase == preset_value.downcase }
    end

    # Get available field types
    def available_fields
      presets.keys
    end

    # Reload presets (useful for development)
    def reload!
      @presets = nil
      presets
    end
  end
end
