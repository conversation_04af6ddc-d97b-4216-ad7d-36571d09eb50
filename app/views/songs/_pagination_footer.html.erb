<div class="bg-white border-t border-gray-200 px-4 py-3">
  <div class="flex items-center justify-between">
    <!-- 记录统计 -->
    <div class="flex-1 flex justify-between sm:hidden">
      <span class="text-sm text-gray-700">
        Showing <%= (@current_page - 1) * @per_page + 1 %>-<%= [@current_page * @per_page, @total_count].min %> of <%= @total_count %>
      </span>
    </div>
    
    <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
      <div>
        <p class="text-sm text-gray-700">
          Showing <span class="font-medium"><%= (@current_page - 1) * @per_page + 1 %></span> to 
          <span class="font-medium"><%= [@current_page * @per_page, @total_count].min %></span> of 
          <span class="font-medium"><%= @total_count %></span> results
        </p>
      </div>
      
      <!-- 分页导航 -->
      <div>
        <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
          <!-- 上一页 -->
          <% if @has_prev %>
            <%= link_to songs_path_with_params(page: @current_page - 1),
              data: { turbo_frame: "song_list_frame" },
              class: "relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50" do %>
              <span class="sr-only">Previous</span>
              <%= flowbite_icon('chevron-left-outline', class: 'w-5 h-5') %>
            <% end %>
          <% else %>
            <span class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-gray-100 text-sm font-medium text-gray-300 cursor-not-allowed">
              <span class="sr-only">Previous</span>
              <%= flowbite_icon('chevron-left-outline', class: 'w-5 h-5') %>
            </span>
          <% end %>

          <!-- 页码 -->
          <% if @total_pages <= 7 %>
            <!-- 显示所有页码 -->
            <% (1..@total_pages).each do |page| %>
              <% if page == @current_page %>
                <span class="relative inline-flex items-center px-4 py-2 border border-blue-500 bg-blue-50 text-sm font-medium text-blue-600">
                  <%= page %>
                </span>
              <% else %>
                <%= link_to page, songs_path_with_params(page: page),
                  data: { turbo_frame: "song_list_frame" },
                  class: "relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50" %>
              <% end %>
            <% end %>
          <% else %>
            <!-- 复杂分页逻辑 -->
            <!-- 首页 -->
            <% if @current_page > 3 %>
              <%= link_to 1, songs_path_with_params(page: 1),
                data: { turbo_frame: "song_list_frame" },
                class: "relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50" %>
              <% if @current_page > 4 %>
                <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700">...</span>
              <% end %>
            <% end %>

            <!-- 当前页附近的页码 -->
            <% start_page = [@current_page - 2, 1].max %>
            <% end_page = [@current_page + 2, @total_pages].min %>
            <% (start_page..end_page).each do |page| %>
              <% if page == @current_page %>
                <span class="relative inline-flex items-center px-4 py-2 border border-blue-500 bg-blue-50 text-sm font-medium text-blue-600">
                  <%= page %>
                </span>
              <% else %>
                <%= link_to page, songs_path_with_params(page: page),
                  data: { turbo_frame: "song_list_frame" },
                  class: "relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50" %>
              <% end %>
            <% end %>

            <!-- 末页 -->
            <% if @current_page < @total_pages - 2 %>
              <% if @current_page < @total_pages - 3 %>
                <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700">...</span>
              <% end %>
              <%= link_to @total_pages, songs_path_with_params(page: @total_pages),
                data: { turbo_frame: "song_list_frame" },
                class: "relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50" %>
            <% end %>
          <% end %>

          <!-- 下一页 -->
          <% if @has_next %>
            <%= link_to songs_path_with_params(page: @current_page + 1),
              data: { turbo_frame: "song_list_frame" },
              class: "relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50" do %>
              <span class="sr-only">Next</span>
              <%= flowbite_icon('chevron-right-outline', class: 'w-5 h-5') %>
            <% end %>
          <% else %>
            <span class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-gray-100 text-sm font-medium text-gray-300 cursor-not-allowed">
              <span class="sr-only">Next</span>
              <%= flowbite_icon('chevron-right-outline', class: 'w-5 h-5') %>
            </span>
          <% end %>
        </nav>
      </div>
    </div>
  </div>
</div>
