<%# 
  Reusable preset search field component
  
  Parameters:
  - field_type: String (genre, mood, instrument, vocal, tpm)
  - label: String (display label)
  - placeholder: String (input placeholder)
  - form: FormBuilder object
  - required: <PERSON><PERSON>an (default: false)
  - multiple: <PERSON><PERSON><PERSON> (default: true for genre/mood/instrument, false for vocal/tpm)
%>
<%
  field_type = local_assigns[:field_type]
  label = local_assigns[:label]
  placeholder = local_assigns[:placeholder]
  form = local_assigns[:form]
  request_id = local_assigns[:request_id]
  required = local_assigns.fetch(:required, false)
  multiple = local_assigns.fetch(:multiple, %w[genre mood instrument].include?(field_type))
  field_name = multiple ? "#{field_type}[]" : field_type
  input_id = "generation_#{field_type}"
  suggestions_id = "#{field_type}_suggestions"
%>
<div class="space-y-2" 
     data-controller="preset-search" 
     data-preset-search-field-type-value="<%= field_type %>"
     data-preset-search-multiple-value="<%= multiple %>">
  <!-- Label -->
  <label for="<%= input_id %>" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
    <%= label %>
    <% if required %>
      <span class="text-red-500 dark:text-red-400">*</span>
    <% end %>
  </label>
  <!-- Search Input -->
  <div>
    <% if %w[vocal tpm].include?(field_type) %>
      <!-- Select dropdown for vocal and tpm -->
      <%= form.select field_type,
                      options_for_select(MusicPresets.all_for_field(field_type).map { |v| [v.humanize, v] }),
                      { include_blank: "Select #{label.downcase}..." },
                      { 
                        id: input_id,
                        class: "w-full px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-300 text-gray-900 dark:text-white text-sm transition-all duration-200",
                        data: {
                          action: "change->preset-search#selectOption"
                        }
                      } %>
    <% else %>
      <!-- Search input for genre, mood, instrument -->
      <%= form.search_field "#{field_type}_search",
                            placeholder: placeholder,
                            id: input_id,
                            class: "w-full px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-300 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 text-sm transition-all duration-200",
                            data: {
                              action: "input->preset-search#updateQuery keydown->preset-search#handleKeydown",
                              preset_search_target: "input"
                            },
                            autocomplete: "off" %>
    <% end %>
  </div>
  <!-- Combined Pills Container (Selected Tags + Suggestions) -->
  <% if multiple %>
    <div id="<%= suggestions_id %>" 
         class="flex flex-wrap gap-1.5 items-center"
         data-preset-search-target="pillsContainer">
      <!-- Initial suggestions will be rendered here, selected tags will be prepended -->
      <% initial_suggestions = request_id.present? ? MusicPresets.stable_recommendations(field_type, request_id) : MusicPresets.popular_for_field(field_type) %>
      <%= render 'preset_suggestions', suggestions: initial_suggestions, field_type: field_type, query: '' %>
    </div>
  <% else %>
    <!-- For non-multiple fields, just show suggestions -->
    <% unless %w[vocal tpm].include?(field_type) %>
      <div id="<%= suggestions_id %>" 
           class="flex flex-wrap gap-1.5 items-center w-full"
           data-preset-search-target="pillsContainer">
        <% initial_suggestions = request_id.present? ? MusicPresets.stable_recommendations(field_type, request_id) : MusicPresets.popular_for_field(field_type) %>
        <%= render 'preset_suggestions', suggestions: initial_suggestions, field_type: field_type, query: '' %>
      </div>
    <% end %>
  <% end %>
  <!-- Hidden fields for selected values -->
  <div data-preset-search-target="hiddenFields" class="hidden"></div>
  <!-- Template for selected tag rendering -->
  <% if multiple %>
    <template data-preset-search-target="tagTemplate">
      <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-gradient-to-r from-purple-100 via-pink-100 to-red-100 text-purple-800 border border-purple-200/50 dark:from-purple-900/20 dark:via-pink-900/20 dark:to-red-900/20 dark:text-purple-300 dark:border-purple-700/30"
            data-tag-type="selected">
        <span data-tag-label></span>
        <button type="button" 
                class="ml-1 inline-flex items-center justify-center w-3 h-3 rounded hover:bg-purple-200 dark:hover:bg-purple-800 focus:outline-none focus:ring-1 focus:ring-purple-500 transition-all duration-200"
                data-action="click->preset-search#removeValue"
          title="">
          <svg class="w-2.5 h-2.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </span>
    </template>
  <% end %>
</div>
