<%# 
  Vocal Type Selection Component
  
  Parameters:
  - form: FormBuilder object
%>
<div class="space-y-3">
  <div class="space-y-1">
    <h3 class="text-sm font-medium text-gray-700 dark:text-gray-300">Vocal Type</h3>
    <p class="text-xs text-gray-500 dark:text-gray-400">Choose the vocal style for your song</p>
  </div>
  <!-- Compact horizontal layout -->
  <div class="grid grid-cols-3 gap-2">
    <!-- Instrumental Option -->
    <label class="relative cursor-pointer">
      <%= form.radio_button :vocal_type, "instrumental", 
                            class: "peer sr-only",
                            checked: false,
                            data: { action: "change->vocal-type#vocalTypeChanged" } %>
      <div class="flex items-center justify-center rounded-lg border-2 border-gray-200 bg-white p-3 text-center transition-all hover:border-purple-300 peer-checked:border-purple-500 peer-checked:bg-gradient-to-r peer-checked:from-purple-50 peer-checked:via-pink-50 peer-checked:to-red-50 peer-focus:ring-2 peer-focus:ring-purple-500 peer-focus:ring-offset-2 dark:border-gray-600 dark:bg-gray-800 dark:hover:border-purple-500 dark:peer-checked:border-purple-400 dark:peer-checked:from-gray-800/50 dark:peer-checked:via-gray-700/30 dark:peer-checked:to-gray-800/50">
        <div class="flex flex-col items-center space-y-1">
          <div class="text-gray-600 peer-checked:text-purple-600 dark:text-gray-400 dark:peer-checked:text-purple-400">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="size-5"><rect width="20" height="16" x="2" y="4" rx="2"/><path d="M6 8h4"/><path d="M14 8h.01"/><path d="M18 8h.01"/><path d="M2 12h20"/><path d="M6 12v4"/><path d="M10 12v4"/><path d="M14 12v4"/><path d="M18 12v4"/></svg>
          </div>
          <span class="text-xs font-medium text-gray-900 dark:text-white">Instrumental</span>
        </div>
      </div>
    </label>
    <!-- Male Vocals Option -->
    <label class="relative cursor-pointer">
      <%= form.radio_button :vocal_type, "male", 
                            class: "peer sr-only",
                            checked: true,
                            data: { action: "change->vocal-type#vocalTypeChanged" } %>
      <div class="flex items-center justify-center rounded-lg border-2 border-gray-200 bg-white p-3 text-center transition-all hover:border-purple-300 peer-checked:border-purple-500 peer-checked:bg-gradient-to-r peer-checked:from-purple-50 peer-checked:via-pink-50 peer-checked:to-red-50 peer-focus:ring-2 peer-focus:ring-purple-500 peer-focus:ring-offset-2 dark:border-gray-600 dark:bg-gray-800 dark:hover:border-purple-500 dark:peer-checked:border-purple-400 dark:peer-checked:from-gray-800/50 dark:peer-checked:via-gray-700/30 dark:peer-checked:to-gray-800/50">
        <div class="flex flex-col items-center space-y-1">
          <div class="text-gray-600 peer-checked:text-purple-600 dark:text-gray-400 dark:peer-checked:text-purple-400">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="size-5"><path d="M16 3h5v5"/><path d="m21 3-6.75 6.75"/><circle cx="10" cy="14" r="6"/></svg>
          </div>
          <span class="text-xs font-medium text-gray-900 dark:text-white">Male Vocals</span>
        </div>
      </div>
    </label>
    <!-- Female Vocals Option -->
    <label class="relative cursor-pointer">
      <%= form.radio_button :vocal_type, "female", 
                            class: "peer sr-only",
                            data: { action: "change->vocal-type#vocalTypeChanged" } %>
      <div class="flex items-center justify-center rounded-lg border-2 border-gray-200 bg-white p-3 text-center transition-all hover:border-purple-300 peer-checked:border-purple-500 peer-checked:bg-gradient-to-r peer-checked:from-purple-50 peer-checked:via-pink-50 peer-checked:to-red-50 peer-focus:ring-2 peer-focus:ring-purple-500 peer-focus:ring-offset-2 dark:border-gray-600 dark:bg-gray-800 dark:hover:border-purple-500 dark:peer-checked:border-purple-400 dark:peer-checked:from-gray-800/50 dark:peer-checked:via-gray-700/30 dark:peer-checked:to-gray-800/50">
        <div class="flex flex-col items-center space-y-1">
          <div class="text-gray-600 peer-checked:text-purple-600 dark:text-gray-400 dark:peer-checked:text-purple-400">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="size-5"><path d="M12 15v7"/><path d="M9 19h6"/><circle cx="12" cy="9" r="6"/></svg>
          </div>
          <span class="text-xs font-medium text-gray-900 dark:text-white">Female Vocals</span>
        </div>
      </div>
    </label>
  </div>
</div>