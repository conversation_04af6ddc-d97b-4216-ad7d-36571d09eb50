<div data-mode-switcher-target="descriptionMode" class="space-y-4">
  <%= form_with url: generations_path, method: :post, 
              scope: :generation,
              data: {
                controller: "generation-form",
              },
              class: "space-y-4" do |form| %>
    <!-- Mode hidden field -->
    <%= form.hidden_field :mode, value: "description" %>
    <!-- Request ID for tab-specific messaging -->
    <%= form.hidden_field :request_id, value: request_id %>
    <!-- Instrumental checkbox -->
    <div class="flex items-center space-x-2">
      <%= form.check_box :instrumental,
                         { class: "w-4 h-4 text-purple-600 bg-white border-gray-300 rounded focus:ring-purple-500 focus:ring-2 dark:bg-gray-700 dark:border-gray-600" },
                         "true", "false" %>
      <%= form.label :instrumental, "Instrumental Only", class: "text-sm font-medium text-gray-700 dark:text-gray-300" %>
    </div>
    <!-- Music Description -->
    <div>
      <%= form.label :prompt, "Music Description", class: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1" %>
      <div data-controller="character-counter" data-character-counter-max-value="400">
        <%= form.text_area :prompt,
                           rows: 3,
                           placeholder: "Describe the music you want...",
                           id: "generation_prompt",
                           maxlength: 400,
                           data: {
                             controller: "auto-resize",
                             "generation-form-target": "promptField",
                             auto_resize_max_height_value: 200
                           },
                           class: "w-full px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-300 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 resize-none text-sm transition-all duration-200" %>
        <div class="text-right mt-1">
          <span class="text-xs text-gray-500 dark:text-gray-400" data-character-counter-target="counter">0/400 characters</span>
        </div>
      </div>
    </div>
    <!-- Random Prompts -->
    <div>
      <%= render "shared/random_prompts_section", prompts: @random_prompts %>
    </div>
    <!-- Submit Section -->
    <%= render "form_footer", form: form, current_credits: current_credits, generation_cost: generation_cost %>
  <% end %>
</div>