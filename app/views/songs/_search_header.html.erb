<div class="p-4 border-b border-purple-200/30 dark:border-gray-700 bg-gradient-to-r from-purple-50/30 via-pink-50/20 to-red-50/30 dark:from-gray-800/50 dark:via-gray-700/30 dark:to-gray-800/50">
  <div class="flex flex-col space-y-3 @lg:flex-row @lg:space-y-0 @lg:space-x-4 @lg:items-center">
    <!-- 搜索框 -->
    <div class="flex-1">
      <%= form_with url: songs_path, method: :get, local: false, data: {
        turbo_frame: "song_list_frame"
      }, class: "flex items-center space-x-2" do |form| %>
        <div class="relative flex-1">
          <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
            <%= flowbite_icon('search-outline', class: 'w-4 h-4 text-gray-500 dark:text-gray-400') %>
          </div>
          <%= form.search_field :search,
            value: params[:search],
            placeholder: "Search by title...",
            class: "block w-full pl-10 pr-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 focus:ring-purple-500 focus:border-purple-500 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 transition-all duration-200" %>
          <!-- 隐藏字段保持其他参数 -->
          <%= form.hidden_field :sort, value: params[:sort] %>
          <%= form.hidden_field :order, value: params[:order] %>
          <%= form.hidden_field :favorites, value: params[:favorites] %>
          <%= form.hidden_field :page, value: 1 %>
        </div>
        <button type="submit" class="px-3 py-2 text-white bg-purple-600 hover:bg-purple-700 dark:bg-purple-500 dark:hover:bg-purple-600 rounded-lg focus:outline-none focus:ring-4 focus:ring-purple-300 dark:focus:ring-purple-800 transition-all duration-200 shadow-sm hover:shadow-md" title="Search">
          <%= flowbite_icon('search-outline', class: 'w-4 h-4') %>
        </button>
        <% if params[:search].present? %>
          <%= link_to songs_path_with_params(search: nil, page: 1),
            data: { turbo_frame: "song_list_frame" },
            class: "px-3 py-2 text-gray-600 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-purple-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-4 focus:ring-purple-200 dark:focus:ring-purple-800 transition-all duration-200",
            title: "Clear search" do %>
            <%= flowbite_icon('x-mark-outline', class: 'w-4 h-4') %>
          <% end %>
        <% end %>
      <% end %>
    </div>
    <!-- 排序和收藏筛选按钮组 -->
    <div class="flex items-center space-x-2">
      <!-- 排序下拉菜单 -->
      <div class="relative">
        <div class="relative inline-block text-left">
          <button type="button" id="sort-dropdown-button" data-dropdown-toggle="sort-dropdown"
            class="inline-flex justify-center px-3 py-2 text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-purple-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-4 focus:ring-purple-200 dark:focus:ring-purple-800 transition-all duration-200"
            title="Sort options">
            <%= flowbite_icon('sort-outline', class: 'w-4 h-4') %>
          </button>
          <div id="sort-dropdown" class="hidden absolute right-0 z-10 mt-2 w-56 bg-white dark:bg-gray-700 rounded-lg shadow-lg border border-gray-200 dark:border-gray-600">
            <div class="py-1">
              <% sort_options.each do |label, options| %>
                <%= link_to label, songs_path_with_params(options.merge(page: 1)),
                  data: { turbo_frame: "song_list_frame" },
                  class: "block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-purple-50 dark:hover:bg-gray-600 #{'bg-purple-50 text-purple-700 dark:bg-purple-900/20 dark:text-purple-300' if current_sort_label == label} transition-all duration-200" %>
              <% end %>
            </div>
          </div>
        </div>
      </div>
      <!-- 收藏筛选按钮 -->
      <div class="relative">
        <%= link_to songs_path_with_params(favorites: params[:favorites] == 'true' ? nil : 'true', page: 1),
          data: { turbo_frame: "song_list_frame" },
          class: "flex items-center px-3 py-2 rounded-lg border #{params[:favorites] == 'true' ? 'bg-red-50 text-red-600 border-red-200 dark:bg-red-900/20 dark:text-red-400 dark:border-red-700' : 'bg-white dark:bg-gray-700 text-gray-600 dark:text-gray-300 border-gray-300 dark:border-gray-600'} hover:bg-purple-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-4 focus:ring-purple-200 dark:focus:ring-purple-800 transition-all duration-200",
          title: params[:favorites] == 'true' ? 'Show all songs' : 'Show favorites only' do %>
          <%= flowbite_icon(params[:favorites] == 'true' ? 'heart-solid' : 'heart-outline', class: 'w-4 h-4') %>
        <% end %>
      </div>
    </div>
  </div>
  <!-- 活动筛选器显示 -->
  <% if params[:search].present? || params[:favorites] == 'true' || params[:sort].present? %>
    <div class="flex flex-wrap items-center gap-2 mt-3">
      <span class="text-sm text-gray-500 dark:text-gray-400">Active filters:</span>
      <% if params[:search].present? %>
        <span class="inline-flex items-center px-2 py-1 text-xs bg-purple-100 text-purple-800 rounded-full dark:bg-purple-900/20 dark:text-purple-300">
          Search: "<%= params[:search] %>"
          <%= link_to songs_path_with_params(search: nil, page: 1),
            data: { turbo_frame: "song_list_frame" },
            class: "ml-1 text-purple-600 hover:text-pink-600 dark:text-purple-400 dark:hover:text-pink-400 transition-all duration-200" do %>
            ×
          <% end %>
        </span>
      <% end %>
      <% if params[:favorites] == 'true' %>
        <span class="inline-flex items-center px-2 py-1 text-xs bg-red-100 text-red-800 rounded-full dark:bg-red-900/20 dark:text-red-300">
          Favorites only
          <%= link_to songs_path_with_params(favorites: nil, page: 1),
            data: { turbo_frame: "song_list_frame" },
            class: "ml-1 text-red-600 hover:text-pink-600 dark:text-red-400 dark:hover:text-pink-400 transition-all duration-200" do %>
            ×
          <% end %>
        </span>
      <% end %>
      <% if params[:sort].present? %>
        <span class="inline-flex items-center px-2 py-1 text-xs bg-purple-100 text-purple-800 rounded-full dark:bg-purple-900/20 dark:text-purple-300">
          Sort: <%= current_sort_label %>
          <%= link_to songs_path_with_params(sort: nil, order: nil, page: 1),
            data: { turbo_frame: "song_list_frame" },
            class: "ml-1 text-purple-600 hover:text-pink-600 dark:text-purple-400 dark:hover:text-pink-400 transition-all duration-200" do %>
            ×
          <% end %>
        </span>
      <% end %>
      <!-- 清除所有筛选器 -->
      <%= link_to "Clear all", songs_path, 
        data: { turbo_frame: "song_list_frame" },
        class: "text-sm text-gray-500 dark:text-gray-400 hover:text-purple-600 dark:hover:text-purple-400 underline transition-all duration-200" %>
    </div>
  <% end %>
</div>
