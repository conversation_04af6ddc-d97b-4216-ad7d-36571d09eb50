<% if authenticated? %>
  <!-- Logged in user menu -->
  <button type="button" class="inline-flex items-center p-2 text-sm font-medium text-gray-600 rounded-lg hover:text-purple-600 dark:hover:text-purple-400 dark:text-gray-400 transition-all duration-200 hover:scale-105 group" id="user-menu-button" aria-expanded="false" data-dropdown-toggle="user-dropdown" data-dropdown-placement="top">
    <div class="size-6 bg-gradient-to-r from-purple-400 via-pink-500 to-red-500 rounded-full flex items-center justify-center mr-2 shadow-md group-hover:shadow-lg transition-shadow duration-200">
      <span class="text-xs font-bold text-white">
        <%= Current.user.username.first.upcase %>
      </span>
    </div>
    <span class="hidden sm:inline font-medium"><%= Current.user.username %></span>
    <%= flowbite_icon('chevron-up-outline', class: 'size-4 ml-1 transition-transform duration-200 group-hover:rotate-180') %>
  </button>
  <!-- User dropdown menu -->
  <div class="z-50 hidden my-4 text-base list-none bg-white rounded-xl divide-y divide-gray-100 shadow-xl border border-gray-200/50 dark:bg-gray-700 dark:divide-gray-600 dark:border-gray-600/50 backdrop-blur-sm" id="user-dropdown">
    <div class="px-4 py-3 bg-gradient-to-r from-purple-50/50 via-pink-50/50 to-red-50/50 dark:from-gray-800/50 dark:via-gray-800/50 dark:to-gray-800/50 rounded-t-xl">
      <span class="block text-sm font-semibold text-gray-900 dark:text-white">@<%= Current.user.username %></span>
      <span class="block text-sm text-gray-600 truncate dark:text-gray-400 mt-1"><%= Current.user.email %></span>
      <span class="inline-flex items-center px-3 py-1 mt-2 rounded-full text-xs font-semibold shadow-sm transition-all duration-200 hover:scale-105 <%= Current.user.plan_type == 'pro' ? 'bg-gradient-to-r from-purple-100 to-pink-100 text-purple-800 dark:from-purple-900/50 dark:to-pink-900/50 dark:text-purple-300' : 'bg-gradient-to-r from-gray-100 to-gray-200 text-gray-800 dark:from-gray-700 dark:to-gray-600 dark:text-gray-300' %>">
        <%= Current.user.plan_type.capitalize %> Member
      </span>
    </div>
    <ul class="py-2" aria-labelledby="user-menu-button">
      <li>
        <%= link_to profile_path, class: "flex items-center px-4 py-3 text-sm font-medium text-gray-700 hover:bg-purple-50 dark:hover:bg-gray-600/50 dark:text-gray-200 dark:hover:text-white transition-all duration-200 hover:scale-105 group" do %>
          <%= flowbite_icon('user-outline', class: 'size-4 mr-3 text-purple-500 group-hover:text-purple-600 transition-colors duration-200') %>
          Profile
        <% end %>
      </li>
      <li>
        <%= link_to logout_path, 
            data: { turbo_method: :delete, turbo_frame: "_top" },
            class: "flex items-center px-4 py-3 text-sm font-medium text-gray-700 hover:bg-red-50 dark:hover:bg-gray-600/50 dark:text-gray-200 dark:hover:text-white transition-all duration-200 hover:scale-105 group rounded-b-xl" do %>
          <%= flowbite_icon('arrow-right-to-bracket-outline', class: 'size-4 mr-3 text-red-500 group-hover:text-red-600 transition-colors duration-200') %>
          Sign out
        <% end %>
      </li>
    </ul>
  </div>
<% else %>
  <!-- Login button for non-authenticated users -->
  <%= link_to login_path, data: { turbo_frame: "modal" }, 
      class: "inline-flex items-center p-2 text-sm font-semibold text-white bg-gradient-to-r from-purple-500 via-pink-500 to-red-500 hover:from-purple-600 hover:via-pink-600 hover:to-red-600 focus:ring-4 focus:ring-purple-200 dark:focus:ring-purple-800 rounded-lg transition-all duration-200 hover:scale-105 shadow-md hover:shadow-lg" do %>
    <%= flowbite_icon('arrow-right-to-bracket-outline', class: 'size-4 mr-1') %>
    <span class="hidden sm:inline">Sign In</span>
  <% end %>
<% end %> 