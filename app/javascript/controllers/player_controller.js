import { Controller } from "@hotwired/stimulus";

// Connects to data-controller="player"
export default class extends Controller {
  static targets = ["player"];

  connect() {
    console.log("Player controller connected");
  }

  disconnect() {}

  // Hide the player and pause playback
  hide() {
    console.log("Hiding player");

    // Try to pause the media player if it exists
    if (this.hasPlayerTarget) {
      this.playerTarget.pause();
    }

    // Hide the entire player container using data-hidden attribute
    this.element.dataset.hidden = "true";
    console.log("Player container hidden");
  }

  // Show the player (can be called when a new song is loaded)
  show() {
    console.log("Showing player");
    if (this.hasContainerTarget) {
      delete this.containerTarget.dataset.hidden;
      console.log("Player container shown");
    }
  }
}
