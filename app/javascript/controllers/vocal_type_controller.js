import { Controller } from "@hotwired/stimulus";

// Connects to data-controller="vocal-type"
export default class extends Controller {
  static targets = ["lyricsSection", "lyricsField"];
  static values = {
    lyricsRequired: { type: Boolean, default: true },
  };

  connect() {
    this.updateLyricsVisibility();
  }

  // Handle vocal type change
  vocalTypeChanged(event) {
    this.updateLyricsVisibility();
  }

  updateLyricsVisibility() {
    const instrumentalRadio = this.element.querySelector(
      'input[value="instrumental"]:checked',
    );
    const isInstrumental = instrumentalRadio !== null;

    if (this.hasLyricsSectionTarget) {
      if (isInstrumental) {
        // Hide lyrics section for instrumental
        this.lyricsSectionTarget.style.display = "none";
        // Clear lyrics field
        if (this.hasLyricsFieldTarget) {
          this.lyricsFieldTarget.value = "";
        }
      } else {
        // Show lyrics section for vocal songs
        this.lyricsSectionTarget.style.display = "block";
      }
    }
  }
}
