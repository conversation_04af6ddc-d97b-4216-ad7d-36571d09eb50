import { Controller } from "@hotwired/stimulus";

export default class extends Controller {
  static targets = ["input", "pillsContainer", "hiddenFields", "tagTemplate"];
  static values = {
    fieldType: String,
    multiple: <PERSON>olean,
  };

  connect() {
    this.selectedValues = [];
    this.searchTimeout = null;
    this.currentQuery = "";
    this.lastSearchQuery = "";

    // Initialize with any existing values
    this.initializeExistingValues();
  }

  disconnect() {
    if (this.searchTimeout) {
      clearTimeout(this.searchTimeout);
    }
  }

  initializeExistingValues() {
    // Check for existing hidden fields and populate selectedValues
    if (this.hasHiddenFieldsTarget) {
      const hiddenInputs = this.hiddenFieldsTarget.querySelectorAll(
        'input[type="hidden"]',
      );
      hiddenInputs.forEach((input) => {
        if (input.value) {
          this.addValue(input.value);
        }
      });

      // Update suggestions to hide any pre-selected values
      if (this.selectedValues.length > 0) {
        this.updateSuggestionsLocally();
      }
    }
  }

  updateQuery(event) {
    this.currentQuery = event.target.value.trim();

    // Only trigger search if query actually changed
    if (this.currentQuery === this.lastSearchQuery) {
      return;
    }

    // Auto-search as user types (with debounce)
    if (this.searchTimeout) {
      clearTimeout(this.searchTimeout);
    }

    this.searchTimeout = setTimeout(() => {
      // Double-check that query still needs to be searched
      if (this.currentQuery !== this.lastSearchQuery) {
        this.loadSuggestions(this.currentQuery);
        this.lastSearchQuery = this.currentQuery;
      }
    }, 300);
  }

  handleKeydown(event) {
    // Prevent Enter key from submitting the main form
    if (event.key === "Enter") {
      event.preventDefault();
      this.handleEnterKey();
    }
  }

  handleEnterKey() {
    if (!this.multipleValue) return;

    const query =
      this.currentQuery ||
      (this.hasInputTarget ? this.inputTarget.value.trim() : "");
    if (!query) return;

    // Check if there are search results available
    const firstSuggestion = this.getFirstSuggestion();

    if (firstSuggestion) {
      // Select the first result
      this.addValue(firstSuggestion);
      this.clearInput();
      this.updateSuggestionsLocally();
    } else {
      // Add as new tag with capitalized first letter
      const capitalizedValue = this.capitalizeFirstLetter(query);
      this.addValue(capitalizedValue);
      this.clearInput();
      this.updateSuggestionsLocally();
    }
  }

  getFirstSuggestion() {
    if (!this.hasPillsContainerTarget) return null;

    // Look for the first suggestion button with a data-value attribute
    const firstSuggestionButton = this.pillsContainerTarget.querySelector(
      '[data-tag-type="suggestion"][data-value]',
    );
    return firstSuggestionButton ? firstSuggestionButton.dataset.value : null;
  }

  capitalizeFirstLetter(text) {
    if (!text) return text;
    return text.charAt(0).toUpperCase() + text.slice(1);
  }

  loadSuggestions(query) {
    const url = new URL("/generations/search_presets", window.location.origin);
    url.searchParams.set("field_type", this.fieldTypeValue);
    url.searchParams.set("query", query);

    // Get request_id from the form's hidden field
    const requestIdField = document.querySelector(
      'input[name="generation[request_id]"]',
    );
    if (requestIdField?.value) {
      url.searchParams.set("request_id", requestIdField.value);
    }

    // using fetch + Turbo.renderStreamMessage is equivalent to form submission and implict handling of the response,
    // see https://turbo.hotwired.dev/reference/streams#processing-stream-elements
    fetch(url, {
      headers: {
        Accept: "text/vnd.turbo-stream.html",
        "X-Requested-With": "XMLHttpRequest",
      },
    })
      .then((response) => response.text())
      .then((html) => {
        // Use Turbo to process the stream response
        Turbo.renderStreamMessage(html);
        // Update lastSearchQuery after successful fetch
        this.lastSearchQuery = query;
      })
      .catch((error) => {
        console.error("Search failed:", error);
      });
  }

  selectSuggestion(event) {
    const value = event.currentTarget.dataset.value;
    if (!value) return;

    if (this.multipleValue) {
      this.addValue(value);
      this.clearInput();
      // Update suggestions locally without network fetch
      this.updateSuggestionsLocally();
    } else {
      this.setValue(value);
    }
  }

  selectOption(event) {
    // For select dropdowns (vocal, tpm)
    const value = event.target.value;
    this.setValue(value);
  }

  addCustom() {
    if (!this.hasInputTarget) return;

    const value = this.currentQuery || this.inputTarget.value.trim();
    if (!value) return;

    // Capitalize first letter for custom values
    const capitalizedValue = this.capitalizeFirstLetter(value);

    if (this.multipleValue) {
      this.addValue(capitalizedValue);
      this.clearInput();
      this.updateSuggestionsLocally();
    } else {
      this.setValue(capitalizedValue);
    }
  }

  addValue(value) {
    if (!this.selectedValues.includes(value)) {
      this.selectedValues.push(value);
      this.updateSelectedTags();
      this.updateHiddenFields();
    }
  }

  setValue(value) {
    this.selectedValues = [value];
    this.updateHiddenFields();
  }

  removeValue(event) {
    event.preventDefault();
    event.stopPropagation();

    const value = event.currentTarget.dataset.value;
    const index = this.selectedValues.indexOf(value);
    if (index > -1) {
      this.selectedValues.splice(index, 1);
      this.updateSelectedTags();
      this.updateHiddenFields();
      // Update suggestions locally without network fetch
      this.updateSuggestionsLocally();
    }
  }

  clearInput() {
    if (this.hasInputTarget) {
      this.inputTarget.value = "";
      const previousQuery = this.currentQuery;
      this.currentQuery = "";

      // Only trigger search if we're actually clearing non-empty input
      if (previousQuery !== "") {
        this.loadSuggestions("");
        this.lastSearchQuery = "";
      }

      this.inputTarget.focus();
    }
  }

  updateSelectedTags() {
    if (!this.hasPillsContainerTarget) return;

    // Remove existing selected tags
    this.pillsContainerTarget
      .querySelectorAll('[data-tag-type="selected"]')
      .forEach((tag) => tag.remove());

    // Create and prepend new selected tags
    this.selectedValues.forEach((value) => {
      const tagElement = this.createTagFromTemplate(value);
      if (tagElement) {
        this.pillsContainerTarget.prepend(tagElement);
      }
    });
  }

  createTagFromTemplate(value) {
    if (!this.hasTagTemplateTarget) return null;

    // Clone the template content
    const tagElement = this.tagTemplateTarget.content.cloneNode(true);

    // Set the data-value attribute on the main tag element
    const mainTagElement = tagElement.querySelector(
      '[data-tag-type="selected"]',
    );
    if (mainTagElement) {
      mainTagElement.dataset.value = value;
    }

    // Set the label text
    const labelElement = tagElement.querySelector("[data-tag-label]");
    if (labelElement) {
      labelElement.textContent = value;
    }

    // Set the remove button attributes
    const removeButton = tagElement.querySelector("button");
    if (removeButton) {
      removeButton.dataset.value = value;
      removeButton.title = `Remove ${value}`;
    }

    return tagElement;
  }

  updateHiddenFields() {
    if (!this.hasHiddenFieldsTarget) return;

    if (this.multipleValue) {
      // Multiple values - create properly indexed array of hidden fields
      this.hiddenFieldsTarget.innerHTML = this.selectedValues
        .map(
          (value, index) => `
        <input type="hidden" name="generation[${this.fieldTypeValue}][]" value="${this.escapeHtml(value)}">
      `,
        )
        .join("");
    } else {
      // Single value - update the visible form field instead of creating hidden fields
      if (this.hasInputTarget) {
        this.inputTarget.value = this.selectedValues[0] || "";
      }
      this.hiddenFieldsTarget.innerHTML = "";
    }
  }

  updateSuggestionsLocally() {
    if (!this.hasPillsContainerTarget) return;

    const selectedValuesSet = new Set(this.selectedValues);

    // Get all current suggestion elements
    const suggestions = this.pillsContainerTarget.querySelectorAll(
      '[data-tag-type="suggestion"]',
    );

    suggestions.forEach((suggestion) => {
      const suggestionValue = suggestion.dataset.value;

      if (selectedValuesSet.has(suggestionValue)) {
        // Hide suggestions that are now selected
        suggestion.style.display = "none";
      } else {
        // Show suggestions that are not selected
        suggestion.style.display = "";
      }
    });
  }

  escapeHtml(text) {
    const div = document.createElement("div");
    div.textContent = text;
    return div.innerHTML;
  }
}
