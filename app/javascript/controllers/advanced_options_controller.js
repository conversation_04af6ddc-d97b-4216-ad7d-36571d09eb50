import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["content", "toggleText", "toggleIcon"]

  connect() {
    this.isExpanded = false
  }

  toggle() {
    this.isExpanded = !this.isExpanded
    
    if (this.isExpanded) {
      this.show()
    } else {
      this.hide()
    }
  }

  show() {
    this.contentTarget.classList.remove('hidden')
    this.toggleTextTarget.textContent = 'Hide Advanced Options'
    this.toggleIconTarget.style.transform = 'rotate(180deg)'
    this.isExpanded = true
  }

  hide() {
    this.contentTarget.classList.add('hidden')
    this.toggleTextTarget.textContent = 'Show Advanced Options'
    this.toggleIconTarget.style.transform = 'rotate(0deg)'
    this.isExpanded = false
  }
}
