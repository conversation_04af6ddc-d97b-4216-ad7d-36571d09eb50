import { Controller } from "@hotwired/stimulus";

export default class extends Controller {
  static targets = ["promptField"];
  static values = { cost: Number };

  connect() {
    console.log("Generation form controller connected");
  }

  // Prevent Enter key from submitting form when pressed in search fields
  preventEnterSubmit(event) {
    // Only prevent if the target is a search input field
    if (event.key === "Enter" && event.target.type === "search") {
      // Let the preset-search controller handle this
      return;
    }
  }

  // Select a random prompt and populate the prompt field
  selectPrompt(event) {
    const prompt = event.currentTarget.dataset.prompt;
    if (this.hasPromptFieldTarget && prompt) {
      this.promptFieldTarget.value = prompt;
      this.promptFieldTarget.focus();

      // Trigger input event for any listeners (like character counter)
      this.promptFieldTarget.dispatchEvent(
        new Event("input", { bubbles: true }),
      );
    }
  }

  // Refresh random prompts
  refreshPrompts(event) {
    // The turbo frame will handle the refresh automatically
    // This method can be used for additional logic if needed
  }
}
