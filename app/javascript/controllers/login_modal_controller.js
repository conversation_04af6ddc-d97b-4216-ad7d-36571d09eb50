import { Controller } from "@hotwired/stimulus";

// import { Modal } from "flowbite";
const Modal = Flowbite.default.Modal;

// Connects to data-controller="login-modal"
export default class extends Controller {
  constructor(element) {
    super(element);
    this.modal = new Modal(this.element);
    console.log("Login modal controller constructor");
  }

  connect() {
    console.log("Login modal controller connected");
    this.modal.show();
  }
}
