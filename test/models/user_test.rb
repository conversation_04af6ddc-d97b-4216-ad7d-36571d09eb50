require "test_helper"

class UserTest < ActiveSupport::TestCase
  test "username normalization converts to lowercase" do
    user = User.new(
      username: "Test<PERSON><PERSON>",
      email: "<EMAIL>",
      plan_type: :free,
      plan_credits_limit: 0
    )
    user.valid?
    assert_equal "testuser", user.username
  end

  test "username normalization replaces spaces and hyphens with underscores" do
    user = User.new(
      username: "test user-name",
      email: "<EMAIL>",
      plan_type: :free,
      plan_credits_limit: 0
    )
    user.valid?
    assert_equal "test_user_name", user.username
  end

  test "username normalization removes invalid characters" do
    user = User.new(
      username: "test@user#name!",
      email: "<EMAIL>",
      plan_type: :free,
      plan_credits_limit: 0
    )
    user.valid?
    assert_equal "testusername", user.username
  end

  test "username normalization removes consecutive underscores" do
    user = User.new(
      username: "test___user__name",
      email: "<EMAIL>",
      plan_type: :free,
      plan_credits_limit: 0
    )
    user.valid?
    assert_equal "test_user_name", user.username
  end

  test "username normalization removes leading and trailing underscores" do
    user = User.new(
      username: "_testuser_",
      email: "<EMAIL>",
      plan_type: :free,
      plan_credits_limit: 0
    )
    user.valid?
    assert_equal "testuser", user.username
  end

  test "username normalization pads short usernames" do
    user = User.new(
      username: "ab",
      email: "<EMAIL>",
      plan_type: :free,
      plan_credits_limit: 0
    )
    user.valid?
    assert user.username.length >= 3
    assert user.username.start_with?("ab")
  end

  test "username normalization truncates long usernames" do
    long_username = "a" * 50
    user = User.new(
      username: long_username,
      email: "<EMAIL>",
      plan_type: :free,
      plan_credits_limit: 0
    )
    user.valid?
    assert_equal 30, user.username.length
  end

  test "email normalization converts to lowercase and strips whitespace" do
    user = User.new(
      username: "testuser",
      email: "  <EMAIL>  ",
      plan_type: :free,
      plan_credits_limit: 0
    )
    user.valid?
    assert_equal "<EMAIL>", user.email
  end

  test "username validation rejects reserved words" do
    reserved_username = "admin"
    user = User.new(
      username: reserved_username,
      email: "<EMAIL>",
      plan_type: :free,
      plan_credits_limit: 0
    )
    assert_not user.valid?
    assert_includes user.errors[:username], "is reserved and cannot be used"
  end

  test "username validation enforces length constraints" do
    # Too short (after normalization)
    user = User.new(
      username: "ab",  # This will be padded to meet minimum
      email: "<EMAIL>",
      plan_type: :free,
      plan_credits_limit: 0
    )
    assert user.valid?  # Should be valid after padding

    # Too long
    long_username = "a" * 35
    user = User.new(
      username: long_username,
      email: "<EMAIL>",
      plan_type: :free,
      plan_credits_limit: 0
    )
    assert user.valid?  # Should be valid after truncation
    assert_equal 30, user.username.length
  end

  test "username validation enforces format constraints" do
    user = User.new(
      username: "test-user!",  # Will be normalized to "test_user"
      email: "<EMAIL>",
      plan_type: :free,
      plan_credits_limit: 0
    )
    assert user.valid?
    assert_equal "test_user", user.username
  end

  test "generate_username_from_email creates valid username" do
    username = User.generate_username_from_email("<EMAIL>")
    assert_match /\A[a-z0-9_]+\z/, username
    assert username.length >= 3
    assert username.length <= 30
  end

  test "generate_username_from_email handles special characters" do
    username = User.generate_username_from_email("test@user!<EMAIL>")
    assert_match /\A[a-z0-9_]+\z/, username
  end

  test "generate_username_from_auth_hash prefers name over email" do
    auth_hash = {
      "info" => {
        "name" => "John Doe",
        "email" => "<EMAIL>"
      }
    }
    username = User.generate_username_from_auth_hash(auth_hash)
    assert_equal "john_doe", username
  end

  test "generate_username_from_auth_hash falls back to email when name is blank" do
    auth_hash = {
      "info" => {
        "name" => "",
        "email" => "<EMAIL>"
      }
    }
    username = User.generate_username_from_auth_hash(auth_hash)
    assert_equal "john", username
  end

  test "generate_username_from_auth_hash generates fallback when both name and email are blank" do
    auth_hash = {
      "info" => {
        "name" => "",
        "email" => ""
      }
    }
    username = User.generate_username_from_auth_hash(auth_hash)
    assert_match /\Auser[a-f0-9]{8}\z/, username
  end

  test "username uniqueness is enforced case-insensitively" do
    # Create first user
    user1 = User.create!(
      username: "testuser",
      email: "<EMAIL>",
      plan_type: :free,
      plan_credits_limit: 0
    )

    # Try to create second user with different case
    user2 = User.new(
      username: "TestUser",  # Will be normalized to "testuser"
      email: "<EMAIL>",
      plan_type: :free,
      plan_credits_limit: 0
    )

    assert_not user2.valid?
    assert_includes user2.errors[:username], "has already been taken"
  end

  test "email cannot be changed after creation" do
    user = User.create!(
      username: "testuser",
      email: "<EMAIL>",
      plan_type: :free,
      plan_credits_limit: 0
    )

    user.email = "<EMAIL>"
    assert_not user.valid?
    assert_includes user.errors[:email], "cannot be changed after account creation"
  end
end
